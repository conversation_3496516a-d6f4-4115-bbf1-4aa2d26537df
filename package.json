{"name": "my-app", "version": "0.1.0", "private": true, "dependencies": {"@fluentui/react": "^8.123.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^12.1.5", "@testing-library/user-event": "^13.5.0", "@types/jest": "^28.1.8", "@types/node": "^14.18.63", "@types/react": "^17.0.87", "@types/react-dom": "^17.0.26", "react": "^17.0.2", "react-dom": "^17.0.2", "react-scripts": "5.0.1", "typescript": "^4.9.5", "web-vitals": "^1.1.2"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}